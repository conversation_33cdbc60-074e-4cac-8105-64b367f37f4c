import { BaseEntity, MealType } from './common';

// 食物条目接口
export interface FoodEntry extends BaseEntity {
  // 基本信息
  name: string;           // 食物名称
  calories: number;       // 卡路里(kcal)
  weight: number;         // 重量(g)
  mealType: MealType;     // 用餐类型
  consumedAt: Date;       // 食用时间
  
  // 来源信息
  source: 'ai' | 'manual' | 'favorite'; // 数据来源
  
  // AI识别信息
  aiRecognition?: {
    confidence: number;   // 置信度(0-1)
    alternatives?: string[]; // 备选识别结果
  };
  
  // 附加信息
  notes?: string;         // 备注
  image?: string;         // 图片(base64或URL)
  isFavorite: boolean;    // 是否收藏
  
  // 营养信息(可选)
  nutrition?: {
    protein?: number;     // 蛋白质(g)
    fat?: number;         // 脂肪(g)
    carbs?: number;       // 碳水化合物(g)
    fiber?: number;       // 膳食纤维(g)
    sugar?: number;       // 糖(g)
  };
}

// 食物识别结果 - 增强版
export interface FoodRecognitionResult {
  foods: {
    name: string;         // 食物名称
    calories: number;     // 卡路里(kcal)
    weight: number;       // 估算重量(g)
    confidence: number;   // 置信度(0-1)
    alternatives?: string[]; // 备选识别结果
    dataSource?: 'nutrition_label' | 'visual_estimation' | 'text_analysis' | 'hybrid'; // 数据来源
    nutrition?: {         // 营养成分信息
      protein: number;    // 蛋白质(g)
      fat: number;        // 脂肪(g)
      carbs: number;      // 碳水化合物(g)
      fiber: number;      // 纤维(g)
      sugar: number;      // 糖分(g)
      sodium: number;     // 钠(mg)
    };
    labelInfo?: {         // 营养标签信息
      hasLabel: boolean;
      servingSize?: string | null;
      labelAccuracy?: number | null;
      brandName?: string | null;
      productName?: string | null;
    };
    portionAnalysis?: {   // 分量分析信息
      estimatedPortion: number;     // 估算食用分量(0-1)
      totalPackageWeight?: number | null;  // 整包重量(g)
      consumedWeight: number;       // 实际食用重量(g)
    };
    imageIndex?: number;  // 图片索引(多图片时使用)
    textAnalysis?: {      // 文本分析信息
      originalPhrase?: string;
      interpretedQuantity?: string;
      cookingMethod?: string | null;
      portionReference?: string;
    } | null;
  }[];
  rawResponse?: any;      // 原始API响应

  // 分析元数据
  analysisMetadata?: {
    hasNutritionLabel: boolean;
    imageQuality: 'high' | 'medium' | 'low' | 'unknown';
    recognitionMethod: 'label_priority' | 'visual_only' | 'hybrid' | 'text_analysis' | 'none';
    processingNotes: string;
    textQuality?: 'high' | 'medium' | 'low';
    ambiguityLevel?: 'low' | 'medium' | 'high';
    extractedFoodCount?: number;
  };

  // 个性化建议
  personalizedAdvice?: string | null;
  exerciseAdvice?: string | null;

  // 多图片分析信息
  multiImageAnalysis?: {
    totalImages: number;
    foodItemsFound: number;
    duplicatesDetected: number;
    overallNutritionSummary: {
      totalCalories: number;
      totalProtein: number;
      totalFat: number;
      totalCarbs: number;
    };
  } | null;
}

// 食物条目创建表单
export interface CreateFoodEntryForm {
  name: string;
  calories: number;
  weight: number;
  mealType: MealType;
  consumedAt: Date;
  notes?: string;
  image?: string;
  isFavorite?: boolean;
  nutrition?: {
    protein?: number;
    fat?: number;
    carbs?: number;
    fiber?: number;
    sugar?: number;
  };
}

// 食物条目更新表单
export interface UpdateFoodEntryForm extends Partial<CreateFoodEntryForm> {}

// 食物条目筛选参数
export interface FoodEntryFilter {
  startDate?: Date;
  endDate?: Date;
  mealType?: MealType;
  minCalories?: number;
  maxCalories?: number;
  isFavorite?: boolean;
  searchTerm?: string;
}