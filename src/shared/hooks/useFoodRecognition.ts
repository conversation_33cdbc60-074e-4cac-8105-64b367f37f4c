import { useState, useCallback } from 'react';
import { MealType } from '@/shared/types';
import { useToast } from '@/shared/hooks/useToast';
import { useAIModelStore } from '@/domains/ai/stores/aiModelStore';
import { geminiService } from '@/infrastructure/ai/geminiService';

export interface FoodRecognitionResult {
  method: 'text' | 'image';
  meal: MealType;
  content: string | undefined;
  timestamp: string;
  personalizedAdvice?: string;  // 个性化建议
  exerciseAdvice?: string;      // 运动建议
  foods: Array<{
    name: string;
    calories: number;
    quantity: string;
    timestamp: string;
    weight: number;       // 重量(g)
    confidence: number;   // 识别置信度(0-1)
    dataSource: 'nutrition_label' | 'visual_estimation'; // 数据来源
    nutrition: {
      protein: number;    // 蛋白质(g)
      fat: number;        // 脂肪(g)
      carbs: number;      // 碳水化合物(g)
      fiber: number;      // 纤维(g)
      sugar: number;      // 糖分(g)
      sodium: number;     // 钠(mg)
    };
    labelInfo?: {
      hasLabel: boolean;
      servingSize?: string;
      labelAccuracy?: number;
    };
  }>;
}

export interface FoodRecognitionState {
  isProcessing: boolean;
  processingStep: string;
  error: string | null;
}

export const useFoodRecognition = () => {
  const [state, setState] = useState<FoodRecognitionState>({
    isProcessing: false,
    processingStep: '',
    error: null
  });

  // AbortController for cancelling requests
  const [abortController, setAbortController] = useState<AbortController | null>(null);

  const { showSuccess, showError } = useToast();
  const { getActiveModel } = useAIModelStore();

  const updateState = useCallback((updates: Partial<FoodRecognitionState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  // 调用活跃模型进行文本分析
  const callActiveModelTextAnalysis = useCallback(async (model: any, text: string) => {
    // 这里需要根据模型类型调用相应的服务
    // 目前先回退到默认服务，后续可以扩展支持多种AI服务
    return await geminiService.analyzeTextFood(text);
  }, []);

  // 调用活跃模型进行图像分析
  const callActiveModelImageAnalysis = useCallback(async (model: any, image: File, context?: string) => {
    // 这里需要根据模型类型调用相应的服务
    // 目前先回退到默认服务，后续可以扩展支持多种AI服务
    return await geminiService.recognizeFood(image, context);
  }, []);

  const startRecognition = useCallback(async (
    method: 'text' | 'image',
    selectedMeal: MealType,
    textInput: string,
    selectedImage: File | null,
    onComplete: (result: FoodRecognitionResult) => void
  ) => {
    updateState({
      isProcessing: true,
      error: null,
      processingStep: '正在连接AI服务...'
    });

    try {
      // 创建新的AbortController
      const controller = new AbortController();
      setAbortController(controller);

      // 检查AI模型配置
      const activeModel = getActiveModel();
      if (!activeModel) {
        // 如果没有配置AI模型，回退到默认的geminiService
        if (!geminiService.isConfigured()) {
          throw new Error('AI服务未配置，请在设置中配置AI模型或检查API密钥');
        }
      }

      let aiResult: any;

      if (method === 'text') {
        // 文本识别
        updateState({ processingStep: '正在分析食物描述...' });
        if (activeModel) {
          // 使用用户选择的AI模型
          aiResult = await callActiveModelTextAnalysis(activeModel, textInput);
        } else {
          // 回退到默认服务
          aiResult = await geminiService.analyzeTextFood(textInput);
        }
      } else {
        // 视觉识别
        if (!selectedImage) {
          throw new Error('请选择要识别的图片');
        }

        // 检查模型是否支持视觉识别
        if (activeModel && !activeModel.supportsVision) {
          throw new Error('当前选择的AI模型不支持图片识别，请选择支持视觉识别的模型');
        }

        updateState({ processingStep: '正在分析食物图片...' });
        // 将textInput作为额外上下文传递给AI
        const additionalContext = textInput?.trim() || undefined;

        if (activeModel) {
          // 使用用户选择的AI模型
          aiResult = await callActiveModelImageAnalysis(activeModel, selectedImage, additionalContext);
        } else {
          // 回退到默认服务
          aiResult = await geminiService.recognizeFood(selectedImage, additionalContext);
        }
      }

      updateState({ processingStep: '正在生成营养记录...' });

      // 转换AI结果为应用格式
      const foods = aiResult.foods.map((food: any) => ({
        name: food.name,
        calories: food.calories,
        quantity: `${Math.round(food.weight)}g`,
        timestamp: new Date().toISOString(),
        dataSource: food.dataSource || 'visual_estimation',
        nutrition: food.nutrition || {
          protein: Math.round(food.calories * 0.15 / 4),
          fat: Math.round(food.calories * 0.25 / 9),
          carbs: Math.round(food.calories * 0.6 / 4),
          fiber: Math.round(food.calories * 0.05 / 4),
          sugar: Math.round(food.calories * 0.1 / 4),
          sodium: Math.round(food.calories * 0.5)
        },
        confidence: food.confidence,
        weight: food.weight,
        labelInfo: food.labelInfo
      }));

      // 检查是否识别到食物
      if (foods.length === 0) {
        const errorMessage = method === 'image'
          ? '未识别到食物内容，请上传清晰的食物图片进行识别'
          : '未从文字中识别到食物信息，请提供更详细的食物描述';

        updateState({
          isProcessing: false,
          error: errorMessage,
          processingStep: ''
        });
        showError(errorMessage);
        return;
      }

      const result: FoodRecognitionResult = {
        method,
        meal: selectedMeal,
        content: method === 'text' ? textInput : selectedImage?.name,
        timestamp: new Date().toISOString(),
        foods: foods
      };

      // 移除识别成功的Toast提示，直接完成识别
      onComplete(result);

    } catch (err) {
      console.error('AI识别失败:', err);
      let errorMessage = '识别失败，请重试';

      if (err instanceof Error) {
        if (err.message.includes('API密钥')) {
          errorMessage = 'AI服务配置错误，请联系管理员';
        } else if (err.message.includes('超时')) {
          errorMessage = '网络超时，请检查网络连接后重试';
        } else if (err.message.includes('API请求失败')) {
          errorMessage = 'AI服务暂时不可用，请稍后重试';
        } else {
          errorMessage = err.message;
        }
      }

      updateState({ error: errorMessage });
      // 移除Toast重复提示，错误信息已在界面中显示
    } finally {
      updateState({
        isProcessing: false,
        processingStep: ''
      });
      // 清理AbortController
      setAbortController(null);
    }
  }, [updateState, showSuccess, showError, getActiveModel, callActiveModelTextAnalysis, callActiveModelImageAnalysis, setAbortController]);

  const stopRecognition = useCallback(() => {
    // 取消正在进行的请求
    if (abortController) {
      abortController.abort();
      setAbortController(null);
    }

    updateState({
      isProcessing: false,
      processingStep: '',
      error: null
    });
    // 移除Toast提示，改为静默关闭
  }, [updateState, abortController]);

  const clearError = useCallback(() => {
    updateState({ error: null });
  }, [updateState]);

  return {
    state,
    startRecognition,
    stopRecognition,
    clearError
  };
};
