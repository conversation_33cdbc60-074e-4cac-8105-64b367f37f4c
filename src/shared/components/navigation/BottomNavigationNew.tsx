import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import FoodRecognitionModal, { RecognitionMethod } from '../modals/FoodRecognitionModal';

interface BottomNavigationProps {
  showAddButton?: boolean;
  onRecognitionComplete?: (result: any) => void;
  currentDate?: Date;
  isAIProcessing?: boolean; // 添加AI处理状态
  onProcessingStateChange?: (isProcessing: boolean) => void; // 添加处理状态回调
}

const BottomNavigation: React.FC<BottomNavigationProps> = ({
  showAddButton = true,
  onRecognitionComplete,
  currentDate,
  isAIProcessing = false,
  onProcessingStateChange
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMethod, setModalMethod] = useState<RecognitionMethod>('text');

  const isActive = (path: string) => location.pathname === path;



  // 处理添加按钮点击
  const handleAddText = () => {
    setModalMethod('text');
    setIsModalOpen(true);
    // 关闭下拉菜单
    const dropdown = document.activeElement as HTMLElement;
    dropdown?.blur();
  };

  const handleAddImage = () => {
    // 关闭下拉菜单
    const dropdown = document.activeElement as HTMLElement;
    dropdown?.blur();

    // 直接打开图片识别界面，使用HTML5 input处理相机调用
    setModalMethod('image');
    setIsModalOpen(true);
  };

  // 处理识别完成
  const handleRecognitionComplete = (result: any) => {
    onRecognitionComplete?.(result);
    setIsModalOpen(false);
  };

  return (
    <>
      <div
        className="dock fixed bottom-0 left-0 right-0 bg-base-100 xl:bg-base-100/95 xl:backdrop-blur-sm"
        style={{
          position: 'fixed',
          zIndex: 1000,
          transform: 'none'
        }}
      >
        {/* 首页 */}
        <button
          onClick={() => navigate('/dashboard')}
          className={`${isActive('/dashboard') ? 'dock-active' : ''} hover:scale-105 transition-transform duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500`}
          aria-label="首页"
          tabIndex={0}
          disabled={isAIProcessing}
          style={isAIProcessing ? { opacity: 0.5, pointerEvents: 'none' } : {}}
        >
          <span className="text-lg xl:text-xl">🏠</span>
          <span className="dock-label text-xs xl:text-sm">首页</span>
        </button>

        {/* 记录 */}
        <button
          onClick={() => navigate('/food-record')}
          className={`${isActive('/food-record') ? 'dock-active' : ''} hover:scale-105 transition-transform duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500`}
          aria-label="开始记录食物"
          tabIndex={0}
          disabled={isAIProcessing}
          style={isAIProcessing ? { opacity: 0.5, pointerEvents: 'none' } : {}}
        >
          <span className="text-lg xl:text-xl">🍽️</span>
          <span className="dock-label text-xs xl:text-sm">记录</span>
        </button>
        
        {/* 中间添加按钮 */}
        {showAddButton && (
          <div className="dropdown dropdown-top dropdown-center">
            <button tabIndex={0} role="button" aria-label="添加食物">
              <span className="text-lg">➕</span>
              <span className="dock-label">添加</span>
            </button>
            <ul tabIndex={0} className="dropdown-content menu bg-white rounded-box z-[9999] w-32 p-1 shadow-lg border border-slate-200 mb-2" style={{ opacity: 1, backgroundColor: 'white' }}>
              <li>
                <button onClick={handleAddText} className="flex items-center justify-center gap-2 py-2 px-4 whitespace-nowrap">
                  <span className="text-base flex-shrink-0">📝</span>
                  <div className="font-medium text-sm text-center">文字识别</div>
                </button>
              </li>
              <li>
                <button onClick={handleAddImage} className="flex items-center justify-center gap-2 py-2 px-4 whitespace-nowrap">
                  <span className="text-base flex-shrink-0">📷</span>
                  <div className="font-medium text-sm text-center">图片识别</div>
                </button>
              </li>
            </ul>
          </div>
        )}
        
        {/* 如果不显示添加按钮，显示占位符 */}
        {!showAddButton && (
          <button disabled aria-label="添加食物">
            <span className="text-lg opacity-30">➕</span>
            <span className="dock-label opacity-30">添加</span>
          </button>
        )}
        
        {/* 日历 */}
        <button
          onClick={() => navigate('/calendar')}
          className={`${isActive('/calendar') ? 'dock-active' : ''} hover:scale-105 transition-transform duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500`}
          aria-label="查看日历"
          tabIndex={0}
          disabled={isAIProcessing}
          style={isAIProcessing ? { opacity: 0.5, pointerEvents: 'none' } : {}}
        >
          <span className="text-lg xl:text-xl">📅</span>
          <span className="dock-label text-xs xl:text-sm">日历</span>
        </button>

        {/* 我的 */}
        <button
          onClick={() => navigate('/profile')}
          className={`${isActive('/profile') ? 'dock-active' : ''} hover:scale-105 transition-transform duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500`}
          aria-label="我的"
          tabIndex={0}
          disabled={isAIProcessing}
          style={isAIProcessing ? { opacity: 0.5, pointerEvents: 'none' } : {}}
        >
          <span className="text-lg xl:text-xl">👤</span>
          <span className="dock-label text-xs xl:text-sm">我的</span>
        </button>
      </div>

      {/* 识别弹窗 */}
      <FoodRecognitionModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        method={modalMethod}
        onRecognitionComplete={handleRecognitionComplete}
        currentDate={currentDate}
        onProcessingStateChange={onProcessingStateChange}
      />
    </>
  );
};

export default BottomNavigation;
