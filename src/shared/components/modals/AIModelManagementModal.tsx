import React, { useState, useEffect } from 'react';
import { useAIModelStore } from '@/domains/ai/stores/aiModelStore';
import { AIModelProvider, AIModelConfig } from '@/shared/types/aiModel';

// 官方模型配置（使用环境变量保护）
const OFFICIAL_MODEL_CONFIG = {
  name: '官方Gemini模型',
  provider: 'gemini' as AIModelProvider,
  modelName: 'gemini-1.5-flash',
  baseUrl: import.meta.env.VITE_GEMINI_BASE_URL || 'http://geminiapi.hinetlove.site:5321',
  apiKey: import.meta.env.VITE_GEMINI_API_KEY || 'gp_md35rcug_6bec23693831739b20e3ad7a80a41e2fcbc62781868b23d3c9d77f1f8a3d2d90',
  supportsVision: true,
  isOfficial: true
};

// 密码哈希（简单示例，生产环境应使用更安全的方法）
const ADMIN_PASSWORD_HASH = 'z12345'; // 管理员密码：z12345
const USER_PASSWORD_HASH = 'xiamu';   // 用户密码：xiamu

interface AIModelManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const AIModelManagementModal: React.FC<AIModelManagementModalProps> = ({
  isOpen,
  onClose,
}) => {
  const { 
    models, 
    activeModelId, 
    addModel, 
    updateModel, 
    deleteModel, 
    setActiveModel,
    validateModel,
    getModelList 
  } = useAIModelStore();

  const [showAddForm, setShowAddForm] = useState(false);
  const [editingModel, setEditingModel] = useState<AIModelConfig | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    provider: 'openai' as AIModelProvider,
    apiKey: '',
    baseUrl: '',
    modelName: '',
  });
  const [availableModels, setAvailableModels] = useState<string[]>([]);
  const [isValidating, setIsValidating] = useState(false);
  const [validationResult, setValidationResult] = useState<string>('');
  const [showOfficialOptions, setShowOfficialOptions] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [passwordType, setPasswordType] = useState<'admin' | 'user'>('user');
  const [passwordInput, setPasswordInput] = useState('');

  // 重置表单
  const resetForm = () => {
    setFormData({
      name: '',
      provider: 'openai',
      apiKey: '',
      baseUrl: '',
      modelName: '',
    });
    setAvailableModels([]);
    setValidationResult('');
    setEditingModel(null);
    setShowOfficialOptions(false);
    setShowPasswordModal(false);
    setPasswordInput('');
  };

  // 验证密码
  const validatePassword = (password: string, type: 'admin' | 'user'): boolean => {
    const hash = type === 'admin' ? ADMIN_PASSWORD_HASH : USER_PASSWORD_HASH;
    return password === hash;
  };

  // 处理官方模型选择
  const handleOfficialModelSelect = (type: 'use' | 'edit') => {
    setPasswordType(type === 'edit' ? 'admin' : 'user');
    setShowPasswordModal(true);
  };

  // 处理密码确认
  const handlePasswordConfirm = () => {
    if (validatePassword(passwordInput, passwordType)) {
      setShowPasswordModal(false);
      setPasswordInput('');

      if (passwordType === 'admin') {
        // 管理员权限：编辑官方模型
        setFormData({
          name: OFFICIAL_MODEL_CONFIG.name,
          provider: OFFICIAL_MODEL_CONFIG.provider,
          apiKey: OFFICIAL_MODEL_CONFIG.apiKey,
          baseUrl: OFFICIAL_MODEL_CONFIG.baseUrl,
          modelName: OFFICIAL_MODEL_CONFIG.modelName,
        });
        setEditingModel({ ...OFFICIAL_MODEL_CONFIG, id: 'official' } as AIModelConfig);
        setShowAddForm(true);
        setShowOfficialOptions(false);
      } else {
        // 用户权限：使用官方模型
        const officialModel = {
          ...OFFICIAL_MODEL_CONFIG,
          id: `official_${Date.now()}`,
        };
        addModel(officialModel);
        setValidationResult('✅ 官方模型添加成功！');
        setShowOfficialOptions(false);
        setTimeout(() => {
          setValidationResult('');
        }, 2000);
      }
    } else {
      setValidationResult('❌ 密码错误，请重试');
      setTimeout(() => {
        setValidationResult('');
      }, 2000);
    }
  };

  // 编辑模型
  const handleEditModel = (model: AIModelConfig) => {
    setEditingModel(model);
    setFormData({
      name: model.name,
      provider: model.provider,
      apiKey: model.apiKey,
      baseUrl: model.baseUrl,
      modelName: model.modelName,
    });
    setShowAddForm(true);
  };

  // 获取模型列表
  const handleGetModels = async () => {
    if (!formData.apiKey || !formData.baseUrl) {
      setValidationResult('请先填写API Key和Base URL');
      return;
    }

    setIsValidating(true);
    try {
      const models = await getModelList(formData.provider, formData.apiKey, formData.baseUrl);
      setAvailableModels(models);
      setValidationResult(`成功获取到 ${models.length} 个模型`);
    } catch (error) {
      setValidationResult('获取模型列表失败: ' + (error instanceof Error ? error.message : '未知错误'));
    } finally {
      setIsValidating(false);
    }
  };

  // 验证模型配置
  const handleValidateModel = async () => {
    if (!formData.modelName) {
      setValidationResult('请选择或输入模型名称');
      return;
    }

    setIsValidating(true);
    try {
      const result = await validateModel(formData);
      if (result.isValid) {
        setValidationResult(`✅ 模型验证成功！${result.capabilities?.supportsVision ? ' 支持视觉识别' : ' 仅支持文本'}`);
      } else {
        setValidationResult(`❌ 验证失败: ${result.error}`);
      }
    } catch (error) {
      setValidationResult('验证失败: ' + (error instanceof Error ? error.message : '未知错误'));
    } finally {
      setIsValidating(false);
    }
  };

  // 保存模型
  const handleSaveModel = async () => {
    if (!formData.name || !formData.apiKey || !formData.baseUrl || !formData.modelName) {
      setValidationResult('请填写完整的配置信息');
      return;
    }

    setIsValidating(true);
    try {
      const result = await validateModel(formData);
      if (!result.isValid) {
        setValidationResult(`❌ 验证失败: ${result.error}`);
        setIsValidating(false);
        return;
      }

      const modelConfig = {
        ...formData,
        supportsVision: result.capabilities?.supportsVision || false,
        isActive: false,
      };

      // 多模态检测警告
      const isMultimodal = result.capabilities?.supportsVision || false;
      let successMessage = '✅ 模型保存成功！';

      if (!isMultimodal) {
        successMessage += '\n⚠️ 该模型不支持图片识别功能，图片识别可能无法正常工作';
      }

      if (editingModel) {
        updateModel(editingModel.id, modelConfig);
      } else {
        addModel(modelConfig);
      }

      setValidationResult(successMessage);
      setTimeout(() => {
        setShowAddForm(false);
        resetForm();
      }, 2000); // 延长显示时间以便用户看到警告
    } catch (error) {
      setValidationResult('保存失败: ' + (error instanceof Error ? error.message : '未知错误'));
    } finally {
      setIsValidating(false);
    }
  };

  // 默认配置
  const getDefaultBaseUrl = (provider: AIModelProvider) => {
    switch (provider) {
      case 'openai':
        return 'https://api.openai.com';
      case 'gemini':
        return 'https://g-proxy.eh.cx';
      default:
        return '';
    }
  };

  // 当provider改变时更新baseUrl
  useEffect(() => {
    if (formData.provider) {
      setFormData(prev => ({
        ...prev,
        baseUrl: getDefaultBaseUrl(prev.provider),
      }));
    }
  }, [formData.provider]);

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center p-4"
      style={{
        backgroundColor: 'rgba(0, 0, 0, 0.4)',
        backdropFilter: 'blur(8px)',
      }}
      onClick={onClose}
    >
      <div
        className="relative bg-white rounded-2xl w-full max-w-sm sm:max-w-md xl:max-w-4xl 2xl:max-w-5xl max-h-[80vh] sm:max-h-[75vh] xl:max-h-[85vh] overflow-hidden shadow-2xl border border-gray-100 flex flex-col"
        style={{
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05)'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 xl:p-8 pb-4 xl:pb-6">
          <div>
            <h2 className="text-xl xl:text-2xl font-bold text-gray-900 mb-1">AI模型管理</h2>
            <p className="text-sm xl:text-base text-gray-500">配置和管理AI模型</p>
          </div>
          <button
            onClick={onClose}
            className="btn btn-error text-white btn-sm xl:btn-md btn-circle hover:bg-red-600 transition-colors focus:outline-none focus:ring-2 focus:ring-red-500"
            tabIndex={0}
          >
            <svg className="w-4 h-4 xl:w-5 xl:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 overflow-y-auto p-6 xl:p-8 pt-2 space-y-6 xl:space-y-8 pb-16 xl:pb-20">
          {!showAddForm ? (
            // 模型列表视图
            <div>
              {/* 只在有模型时显示头部和添加按钮 */}
              {models.length > 0 && (
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-gray-900">已配置的模型</h3>
                  <div className="flex gap-2">
                    <button
                      onClick={() => handleOfficialModelSelect('use')}
                      className="btn btn-primary btn-sm text-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200"
                    >
                      官方模型
                    </button>
                    <button
                      onClick={() => setShowAddForm(true)}
                      className="btn btn-success text-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200"
                    >
                      + 自定义模型
                    </button>
                  </div>
                </div>
              )}

              {models.length === 0 ? (
                <div className="text-center py-16">
                  <div className="text-6xl mb-6">🤖</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">开始配置AI模型</h3>
                  <p className="text-gray-500 mb-6 max-w-md mx-auto">
                    配置AI模型后，您就可以使用智能食物识别和营养分析功能了。
                    支持文本识别和图片识别两种方式。
                  </p>

                  {/* 官方模型和自定义模型选项 */}
                  <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-6">
                    <button
                      onClick={() => handleOfficialModelSelect('use')}
                      className="btn btn-primary text-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px] px-8"
                    >
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      使用官方模型
                    </button>
                    <button
                      onClick={() => setShowAddForm(true)}
                      className="btn btn-outline rounded-xl border-gray-300 hover:bg-gray-100 transition-all duration-200 min-h-[44px] px-8"
                    >
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      自定义模型
                    </button>
                  </div>

                  <p className="text-sm text-gray-400">
                    推荐使用官方模型，配置简单且稳定可靠
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {models.map((model) => (
                    <div
                      key={model.id}
                      className={`border rounded-xl p-4 ${
                        model.id === activeModelId ? 'border-emerald-500 bg-emerald-50' : 'border-gray-200'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h4 className="font-semibold text-gray-900">{model.name}</h4>
                            {model.id === activeModelId && (
                              <span className="bg-emerald-100 text-emerald-800 text-xs px-2 py-1 rounded-full">
                                当前使用
                              </span>
                            )}
                            {model.supportsVision && (
                              <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                                支持图像
                              </span>
                            )}
                          </div>
                          <div className="text-sm text-gray-600">
                            <p>提供商: {model.provider.toUpperCase()}</p>
                            <p>模型: {model.modelName}</p>
                            <p>地址: {model.baseUrl}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {model.id !== activeModelId && (
                            <button
                              onClick={() => setActiveModel(model.id)}
                              className="btn btn-success btn-sm text-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200"
                            >
                              设为当前
                            </button>
                          )}
                          <button
                            onClick={() => handleEditModel(model)}
                            className="btn btn-primary btn-sm text-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200"
                          >
                            编辑
                          </button>
                          <button
                            onClick={() => deleteModel(model.id)}
                            className="btn btn-error btn-sm text-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200"
                          >
                            删除
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ) : (
            // 添加/编辑表单视图
            <div>
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">
                  {editingModel ? '编辑模型' : '添加新模型'}
                </h3>
                <button
                  onClick={() => {
                    setShowAddForm(false);
                    resetForm();
                  }}
                  className="btn btn-outline btn-sm rounded-xl border-gray-300 hover:bg-gray-100 transition-all duration-200"
                >
                  返回列表
                </button>
              </div>

              <div className="space-y-6">
                {/* 基本信息 */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      模型名称 *
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                      placeholder="例如: GPT-4 Vision"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      提供商 *
                    </label>
                    <select
                      value={formData.provider}
                      onChange={(e) => setFormData(prev => ({ ...prev, provider: e.target.value as AIModelProvider }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    >
                      <option value="openai">OpenAI</option>
                      <option value="gemini">Google Gemini</option>
                    </select>
                  </div>
                </div>

                {/* API配置 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    API Key *
                  </label>
                  <input
                    type="password"
                    value={formData.apiKey}
                    onChange={(e) => setFormData(prev => ({ ...prev, apiKey: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    placeholder="输入API密钥"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Base URL *
                  </label>
                  <input
                    type="url"
                    value={formData.baseUrl}
                    onChange={(e) => setFormData(prev => ({ ...prev, baseUrl: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    placeholder="API服务地址"
                  />
                </div>

                {/* 模型选择 */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm font-medium text-gray-700">
                      模型名称 *
                    </label>
                    <button
                      onClick={handleGetModels}
                      disabled={isValidating}
                      className="text-sm text-emerald-600 hover:text-emerald-700 disabled:opacity-50"
                    >
                      {isValidating ? '获取中...' : '获取可用模型'}
                    </button>
                  </div>
                  
                  {availableModels.length > 0 ? (
                    <select
                      value={formData.modelName}
                      onChange={(e) => setFormData(prev => ({ ...prev, modelName: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    >
                      <option value="">选择模型</option>
                      {availableModels.map((model) => (
                        <option key={model} value={model}>
                          {model}
                        </option>
                      ))}
                    </select>
                  ) : (
                    <input
                      type="text"
                      value={formData.modelName}
                      onChange={(e) => setFormData(prev => ({ ...prev, modelName: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                      placeholder="手动输入模型名称，如: gpt-4-vision-preview"
                    />
                  )}
                </div>

                {/* 验证结果 */}
                {validationResult && (
                  <div className={`p-3 rounded-lg text-sm ${
                    validationResult.includes('✅') ? 'bg-green-50 text-green-800' :
                    validationResult.includes('❌') ? 'bg-red-50 text-red-800' :
                    'bg-blue-50 text-blue-800'
                  }`}>
                    <div className="whitespace-pre-line">
                      {validationResult}
                    </div>
                  </div>
                )}

                {/* 操作按钮 */}
                <div className="absolute bottom-0 left-0 right-0 flex gap-3 p-4 pt-3 bg-gray-50 rounded-b-2xl border-t border-gray-100">
                  <button
                    onClick={handleValidateModel}
                    disabled={isValidating}
                    className="btn btn-primary text-white flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px] disabled:opacity-50"
                  >
                    {isValidating ? '验证中...' : '验证配置'}
                  </button>
                  <button
                    onClick={handleSaveModel}
                    disabled={isValidating}
                    className="btn btn-success text-white flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px] disabled:opacity-50"
                  >
                    {isValidating ? '保存中...' : '保存模型'}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>



      {/* 密码确认模态窗 */}
      {showPasswordModal && (
        <div
          className="fixed inset-0 z-[70] flex items-center justify-center p-4"
          style={{
            backgroundColor: 'rgba(0, 0, 0, 0.4)',
            backdropFilter: 'blur(8px)',
          }}
          onClick={(e) => {
            e.stopPropagation();
            setShowPasswordModal(false);
            setPasswordInput('');
          }}
        >
          <div
            className="relative bg-white rounded-2xl w-full max-w-sm shadow-2xl border border-gray-100 flex flex-col"
            style={{
              boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05)'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* 头部 */}
            <div className="flex items-center justify-between p-6 pb-4">
              <div>
                <h3 className="text-xl font-bold text-gray-900 mb-1">
                  {passwordType === 'admin' ? '管理员验证' : '用户验证'}
                </h3>
                <p className="text-sm text-gray-500">
                  {passwordType === 'admin'
                    ? '请输入管理员密码以编辑官方模型配置'
                    : '请输入用户密码以使用官方模型'
                  }
                </p>
              </div>
              <button
                onClick={() => {
                  setShowPasswordModal(false);
                  setPasswordInput('');
                }}
                className="btn btn-error text-white btn-sm xl:btn-md btn-circle hover:bg-red-600 transition-colors focus:outline-none focus:ring-2 focus:ring-red-500"
                tabIndex={0}
              >
                <svg className="w-4 h-4 xl:w-5 xl:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* 内容 */}
            <div className="flex-1 p-6 pt-2 pb-16">
              <input
                type="password"
                placeholder="请输入密码"
                value={passwordInput}
                onChange={(e) => setPasswordInput(e.target.value)}
                className="input input-bordered w-full rounded-xl"
                onKeyPress={(e) => e.key === 'Enter' && handlePasswordConfirm()}
                autoFocus
              />
            </div>

            {/* 底部按钮 */}
            <div className="absolute bottom-0 left-0 right-0 flex gap-3 p-4 pt-3 bg-gray-50 rounded-b-2xl border-t border-gray-100">
              <button
                onClick={handlePasswordConfirm}
                className="btn btn-primary text-white flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px]"
              >
                确认
              </button>
              <button
                onClick={() => {
                  setShowPasswordModal(false);
                  setPasswordInput('');
                }}
                className="btn btn-outline flex-1 rounded-xl border-gray-300 hover:bg-gray-100 transition-all duration-200 min-h-[44px]"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AIModelManagementModal;
